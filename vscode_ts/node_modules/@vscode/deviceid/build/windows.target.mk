# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := windows
### Rules for final target.
LDFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LDFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LIBS :=

$(builddir)/windows.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/windows.node: LIBS := $(LIBS)
$(builddir)/windows.node: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/windows.node: TOOLSET := $(TOOLSET)
$(builddir)/windows.node:  FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(builddir)/windows.node
# Add target alias
.PHONY: windows
windows: $(builddir)/windows.node

# Short alias for building this executable.
.PHONY: windows.node
windows.node: $(builddir)/windows.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/windows.node

