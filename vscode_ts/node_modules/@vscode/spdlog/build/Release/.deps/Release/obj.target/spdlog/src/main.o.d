cmd_Release/obj.target/spdlog/src/main.o := c++ -o Release/obj.target/spdlog/src/main.o ../src/main.cc '-DNODE_GYP_MODULE_NAME=spdlog' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-DELECTRON_ENSURE_CONFIG_GYPI' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DUSING_ELECTRON_CONFIG_GYPI' '-DV8_COMPRESS_POINTERS' '-DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE' '-DV8_31BIT_SMIS_ON_64BIT_ARCH' '-DV8_ENABLE_SANDBOX' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DOPENSSL_NO_ASM' '-DNODE_API_SWALLOW_UNTHROWABLE_EXCEPTIONS' '-DNAPI_CPP_EXCEPTIONS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include -I../../../node-addon-api -I../deps/spdlog/include  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=10.7 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++20 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/spdlog/src/main.o.d.raw   -c
Release/obj.target/spdlog/src/main.o: ../src/main.cc \
  ../../../node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h \
  ../../../node-addon-api/napi-inl.h \
  ../../../node-addon-api/napi-inl.deprecated.h ../src/logger.h \
  ../deps/spdlog/include/spdlog/spdlog.h \
  ../deps/spdlog/include/spdlog/common.h \
  ../deps/spdlog/include/spdlog/tweakme.h \
  ../deps/spdlog/include/spdlog/details/null_mutex.h \
  ../deps/spdlog/include/spdlog/fmt/fmt.h \
  ../deps/spdlog/include/spdlog/fmt/bundled/core.h \
  ../deps/spdlog/include/spdlog/fmt/bundled/format.h \
  ../deps/spdlog/include/spdlog/fmt/bundled/format-inl.h \
  ../deps/spdlog/include/spdlog/common-inl.h \
  ../deps/spdlog/include/spdlog/details/registry.h \
  ../deps/spdlog/include/spdlog/details/periodic_worker.h \
  ../deps/spdlog/include/spdlog/details/periodic_worker-inl.h \
  ../deps/spdlog/include/spdlog/details/registry-inl.h \
  ../deps/spdlog/include/spdlog/logger.h \
  ../deps/spdlog/include/spdlog/details/log_msg.h \
  ../deps/spdlog/include/spdlog/details/log_msg-inl.h \
  ../deps/spdlog/include/spdlog/details/os.h \
  ../deps/spdlog/include/spdlog/details/os-inl.h \
  ../deps/spdlog/include/spdlog/details/backtracer.h \
  ../deps/spdlog/include/spdlog/details/log_msg_buffer.h \
  ../deps/spdlog/include/spdlog/details/log_msg_buffer-inl.h \
  ../deps/spdlog/include/spdlog/details/circular_q.h \
  ../deps/spdlog/include/spdlog/details/backtracer-inl.h \
  ../deps/spdlog/include/spdlog/logger-inl.h \
  ../deps/spdlog/include/spdlog/sinks/sink.h \
  ../deps/spdlog/include/spdlog/formatter.h \
  ../deps/spdlog/include/spdlog/sinks/sink-inl.h \
  ../deps/spdlog/include/spdlog/pattern_formatter.h \
  ../deps/spdlog/include/spdlog/pattern_formatter-inl.h \
  ../deps/spdlog/include/spdlog/details/fmt_helper.h \
  ../deps/spdlog/include/spdlog/sinks/ansicolor_sink.h \
  ../deps/spdlog/include/spdlog/details/console_globals.h \
  ../deps/spdlog/include/spdlog/sinks/ansicolor_sink-inl.h \
  ../deps/spdlog/include/spdlog/version.h \
  ../deps/spdlog/include/spdlog/details/synchronous_factory.h \
  ../deps/spdlog/include/spdlog/spdlog-inl.h
../src/main.cc:
../../../node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h:
../../../node-addon-api/napi-inl.h:
../../../node-addon-api/napi-inl.deprecated.h:
../src/logger.h:
../deps/spdlog/include/spdlog/spdlog.h:
../deps/spdlog/include/spdlog/common.h:
../deps/spdlog/include/spdlog/tweakme.h:
../deps/spdlog/include/spdlog/details/null_mutex.h:
../deps/spdlog/include/spdlog/fmt/fmt.h:
../deps/spdlog/include/spdlog/fmt/bundled/core.h:
../deps/spdlog/include/spdlog/fmt/bundled/format.h:
../deps/spdlog/include/spdlog/fmt/bundled/format-inl.h:
../deps/spdlog/include/spdlog/common-inl.h:
../deps/spdlog/include/spdlog/details/registry.h:
../deps/spdlog/include/spdlog/details/periodic_worker.h:
../deps/spdlog/include/spdlog/details/periodic_worker-inl.h:
../deps/spdlog/include/spdlog/details/registry-inl.h:
../deps/spdlog/include/spdlog/logger.h:
../deps/spdlog/include/spdlog/details/log_msg.h:
../deps/spdlog/include/spdlog/details/log_msg-inl.h:
../deps/spdlog/include/spdlog/details/os.h:
../deps/spdlog/include/spdlog/details/os-inl.h:
../deps/spdlog/include/spdlog/details/backtracer.h:
../deps/spdlog/include/spdlog/details/log_msg_buffer.h:
../deps/spdlog/include/spdlog/details/log_msg_buffer-inl.h:
../deps/spdlog/include/spdlog/details/circular_q.h:
../deps/spdlog/include/spdlog/details/backtracer-inl.h:
../deps/spdlog/include/spdlog/logger-inl.h:
../deps/spdlog/include/spdlog/sinks/sink.h:
../deps/spdlog/include/spdlog/formatter.h:
../deps/spdlog/include/spdlog/sinks/sink-inl.h:
../deps/spdlog/include/spdlog/pattern_formatter.h:
../deps/spdlog/include/spdlog/pattern_formatter-inl.h:
../deps/spdlog/include/spdlog/details/fmt_helper.h:
../deps/spdlog/include/spdlog/sinks/ansicolor_sink.h:
../deps/spdlog/include/spdlog/details/console_globals.h:
../deps/spdlog/include/spdlog/sinks/ansicolor_sink-inl.h:
../deps/spdlog/include/spdlog/version.h:
../deps/spdlog/include/spdlog/details/synchronous_factory.h:
../deps/spdlog/include/spdlog/spdlog-inl.h:
