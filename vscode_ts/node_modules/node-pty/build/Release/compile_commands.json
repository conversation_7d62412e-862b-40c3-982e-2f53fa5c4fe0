[{"command": "c++ \"-DNODE_GYP_MODULE_NAME=pty\" \"-DUSING_UV_SHARED=1\" \"-DUSING_V8_SHARED=1\" \"-DV8_DEPRECATION_WARNINGS=1\" \"-D_GLIBCXX_USE_CXX11_ABI=1\" -DELECTRON_ENSURE_CONFIG_GYPI \"-D_DARWIN_USE_64_BIT_INODE=1\" -D_LARGEFILE_SOURCE \"-D_FILE_OFFSET_BITS=64\" -DUSING_ELECTRON_CONFIG_GYPI -DV8_COMPRESS_POINTERS -DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE -DV8_31BIT_SMIS_ON_64BIT_ARCH -DV8_ENABLE_SANDBOX -DOPENSSL_NO_PINSHARED -DOPENSSL_THREADS -DOPENSSL_NO_ASM -DNAPI_CPP_EXCEPTIONS -DBUILDING_NODE_EXTENSION -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include -I/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/node-addon-api -O3 -gdwarf-2 -fno-strict-aliasing \"-mmacosx-version-min=10.7\" -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter \"-std=gnu++20\" \"-stdlib=libc++\" -fno-rtti -c /Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/node-pty/src/unix/pty.cc", "directory": ".", "file": "/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/node-pty/src/unix/pty.cc"}, {"command": "c++ \"-DNODE_GYP_MODULE_NAME=spawn-helper\" \"-DUSING_UV_SHARED=1\" \"-DUSING_V8_SHARED=1\" \"-DV8_DEPRECATION_WARNINGS=1\" \"-D_GLIBCXX_USE_CXX11_ABI=1\" -DELECTRON_ENSURE_CONFIG_GYPI \"-D_DARWIN_USE_64_BIT_INODE=1\" -D_LARGEFILE_SOURCE \"-D_FILE_OFFSET_BITS=64\" -DUSING_ELECTRON_CONFIG_GYPI -DV8_COMPRESS_POINTERS -DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE -DV8_31BIT_SMIS_ON_64BIT_ARCH -DV8_ENABLE_SANDBOX -DOPENSSL_NO_PINSHARED -DOPENSSL_THREADS -DOPENSSL_NO_ASM -DNAPI_CPP_EXCEPTIONS -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include -I/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/node-addon-api -O3 -gdwarf-2 -fno-strict-aliasing \"-mmacosx-version-min=10.7\" -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter \"-std=gnu++20\" \"-stdlib=libc++\" -fno-rtti -c /Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/node-pty/src/unix/spawn-helper.cc", "directory": ".", "file": "/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/node-pty/src/unix/spawn-helper.cc"}]