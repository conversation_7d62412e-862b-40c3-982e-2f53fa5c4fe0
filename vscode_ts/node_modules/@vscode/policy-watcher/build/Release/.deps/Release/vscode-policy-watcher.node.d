cmd_Release/vscode-policy-watcher.node := c++ -bundle -undefined dynamic_lookup -Wl,-search_paths_first -mmacosx-version-min=10.7 -arch arm64 -L./Release -stdlib=libc++  -o Release/vscode-policy-watcher.node Release/obj.target/vscode-policy-watcher/src/main.o Release/obj.target/vscode-policy-watcher/src/macos/PolicyWatcher.o Release/obj.target/vscode-policy-watcher/src/macos/StringPolicy.o Release/obj.target/vscode-policy-watcher/src/macos/NumberPolicy.o Release/obj.target/vscode-policy-watcher/src/macos/BooleanPolicy.o 
