cmd_Release/watcher.node := c++ -bundle -undefined dynamic_lookup -Wl,-search_paths_first -mmacosx-version-min=11.0 -arch arm64 -L./Release -stdlib=libc++  -o Release/watcher.node Release/obj.target/watcher/src/binding.o Release/obj.target/watcher/src/Watcher.o Release/obj.target/watcher/src/Backend.o Release/obj.target/watcher/src/DirTree.o Release/obj.target/watcher/src/Glob.o Release/obj.target/watcher/src/Debounce.o Release/obj.target/watcher/src/watchman/BSER.o Release/obj.target/watcher/src/watchman/WatchmanBackend.o Release/obj.target/watcher/src/shared/BruteForceBackend.o Release/obj.target/watcher/src/unix/fts.o Release/obj.target/watcher/src/macos/FSEventsBackend.o Release/obj.target/watcher/src/kqueue/KqueueBackend.o -framework CoreServices
