# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := winregistry
### Rules for final target.
LDFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LDFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LIBS :=

$(builddir)/winregistry.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/winregistry.node: LIBS := $(LIBS)
$(builddir)/winregistry.node: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/winregistry.node: TOOLSET := $(TOOLSET)
$(builddir)/winregistry.node:  FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(builddir)/winregistry.node
# Add target alias
.PHONY: winregistry
winregistry: $(builddir)/winregistry.node

# Short alias for building this executable.
.PHONY: winregistry.node
winregistry.node: $(builddir)/winregistry.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/winregistry.node

