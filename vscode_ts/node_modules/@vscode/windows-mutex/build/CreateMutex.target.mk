# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := CreateMutex
### Rules for final target.
LDFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=10.7 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LDFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=10.7 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LIBS :=

$(builddir)/CreateMutex.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/CreateMutex.node: LIBS := $(LIBS)
$(builddir)/CreateMutex.node: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/CreateMutex.node: TOOLSET := $(TOOLSET)
$(builddir)/CreateMutex.node:  FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(builddir)/CreateMutex.node
# Add target alias
.PHONY: CreateMutex
CreateMutex: $(builddir)/CreateMutex.node

# Short alias for building this executable.
.PHONY: CreateMutex.node
CreateMutex.node: $(builddir)/CreateMutex.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/CreateMutex.node

