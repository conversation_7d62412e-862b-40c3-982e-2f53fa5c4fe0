cmd_Release/obj.target/vscode-sqlite3/src/node_sqlite3.o := c++ -o Release/obj.target/vscode-sqlite3/src/node_sqlite3.o ../src/node_sqlite3.cc '-DNODE_GYP_MODULE_NAME=vscode-sqlite3' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-DELECTRON_ENSURE_CONFIG_GYPI' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DUSING_ELECTRON_CONFIG_GYPI' '-DV8_COMPRESS_POINTERS' '-DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE' '-DV8_31BIT_SMIS_ON_64BIT_ARCH' '-DV8_ENABLE_SANDBOX' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DOPENSSL_NO_ASM' '-DNODE_API_SWALLOW_UNTHROWABLE_EXCEPTIONS' '-DNAPI_CPP_EXCEPTIONS' '-DSQLITE_THREADSAFE=1' '-DHAVE_USLEEP=1' '-DSQLITE_ENABLE_FTS3' '-DSQLITE_ENABLE_FTS4' '-DSQLITE_ENABLE_FTS5' '-DSQLITE_ENABLE_RTREE' '-DSQLITE_ENABLE_DBSTAT_VTAB=1' '-DSQLITE_ENABLE_MATH_FUNCTIONS' '-DBUILDING_NODE_EXTENSION' '-DNDEBUG' -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include -I/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/@vscode/sqlite3/node_modules/node-addon-api -I../node_modules/node-addon-api -I./Release/obj/gen/sqlite-autoconf-3390400  -O3 -fno-strict-aliasing -mmacosx-version-min=10.7 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++20 -stdlib=libc++ -fno-rtti -fvisibility-inlines-hidden -MMD -MF ./Release/.deps/Release/obj.target/vscode-sqlite3/src/node_sqlite3.o.d.raw   -c
Release/obj.target/vscode-sqlite3/src/node_sqlite3.o: \
  ../src/node_sqlite3.cc \
  Release/obj/gen/sqlite-autoconf-3390400/sqlite3.h ../src/macros.h \
  /Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/@vscode/sqlite3/node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h \
  /Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/@vscode/sqlite3/node_modules/node-addon-api/napi-inl.h \
  /Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/@vscode/sqlite3/node_modules/node-addon-api/napi-inl.deprecated.h \
  ../src/database.h ../src/async.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/errno.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/version.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/unix.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/threadpool.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/darwin.h \
  ../src/threading.h ../src/statement.h ../src/backup.h
../src/node_sqlite3.cc:
Release/obj/gen/sqlite-autoconf-3390400/sqlite3.h:
../src/macros.h:
/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/@vscode/sqlite3/node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h:
/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/@vscode/sqlite3/node_modules/node-addon-api/napi-inl.h:
/Users/<USER>/yudaprama/kawai-agent/vscode_ts/node_modules/@vscode/sqlite3/node_modules/node-addon-api/napi-inl.deprecated.h:
../src/database.h:
../src/async.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/errno.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/version.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/unix.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/threadpool.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/uv/darwin.h:
../src/threading.h:
../src/statement.h:
../src/backup.h:
