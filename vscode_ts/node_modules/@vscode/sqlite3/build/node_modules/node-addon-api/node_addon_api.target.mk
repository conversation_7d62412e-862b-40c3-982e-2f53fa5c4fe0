# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := node_addon_api
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=node_addon_api' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-DELECTRON_ENSURE_CONFIG_GYPI' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DUSING_ELECTRON_CONFIG_GYPI' \
	'-DV8_COMPRESS_POINTERS' \
	'-DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE' \
	'-DV8_31BIT_SMIS_ON_64BIT_ARCH' \
	'-DV8_ENABLE_SANDBOX' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DOPENSSL_NO_ASM' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-O0 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-std=gnu++20 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Debug :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Debug :=

INCS_Debug := \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=node_addon_api' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-DELECTRON_ENSURE_CONFIG_GYPI' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DUSING_ELECTRON_CONFIG_GYPI' \
	'-DV8_COMPRESS_POINTERS' \
	'-DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE' \
	'-DV8_31BIT_SMIS_ON_64BIT_ARCH' \
	'-DV8_ENABLE_SANDBOX' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DOPENSSL_NO_ASM'

# Flags passed to all source files.
CFLAGS_Release := \
	-O3 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-std=gnu++20 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Release :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Release :=

INCS_Release := \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include

OBJS :=

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)


### Rules for final target.
$(obj).target/node_modules/node-addon-api/node_addon_api.stamp: TOOLSET := $(TOOLSET)
$(obj).target/node_modules/node-addon-api/node_addon_api.stamp:  FORCE_DO_CMD
	$(call do_cmd,touch)

all_deps += $(obj).target/node_modules/node-addon-api/node_addon_api.stamp
# Add target alias
.PHONY: node_addon_api
node_addon_api: $(obj).target/node_modules/node-addon-api/node_addon_api.stamp

