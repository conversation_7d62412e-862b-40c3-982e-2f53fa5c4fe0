cmd_Release/obj.target/sqlite3/gen/sqlite-autoconf-3390400/sqlite3.o := cc -o Release/obj.target/sqlite3/gen/sqlite-autoconf-3390400/sqlite3.o Release/obj/gen/sqlite-autoconf-3390400/sqlite3.c '-DNODE_GYP_MODULE_NAME=sqlite3' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-DELECTRON_ENSURE_CONFIG_GYPI' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DUSING_ELECTRON_CONFIG_GYPI' '-DV8_COMPRESS_POINTERS' '-DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE' '-DV8_31BIT_SMIS_ON_64BIT_ARCH' '-DV8_ENABLE_SANDBOX' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DOPENSSL_NO_ASM' '-D_REENTRANT=1' '-DSQLITE_THREADSAFE=1' '-DHAVE_USLEEP=1' '-DSQLITE_ENABLE_FTS3' '-DSQLITE_ENABLE_FTS4' '-DSQLITE_ENABLE_FTS5' '-DSQLITE_ENABLE_RTREE' '-DSQLITE_ENABLE_DBSTAT_VTAB=1' '-DSQLITE_ENABLE_MATH_FUNCTIONS' '-DNDEBUG' -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include -I./Release/obj/gen/sqlite-autoconf-3390400  -O3 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter  -MMD -MF ./Release/.deps/Release/obj.target/sqlite3/gen/sqlite-autoconf-3390400/sqlite3.o.d.raw   -c
Release/obj.target/sqlite3/gen/sqlite-autoconf-3390400/sqlite3.o: \
  Release/obj/gen/sqlite-autoconf-3390400/sqlite3.c
Release/obj/gen/sqlite-autoconf-3390400/sqlite3.c:
