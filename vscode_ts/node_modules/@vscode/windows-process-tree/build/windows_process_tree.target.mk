# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := windows_process_tree
### Rules for final target.
LDFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=10.7 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LDFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=10.7 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LIBS :=

$(builddir)/windows_process_tree.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/windows_process_tree.node: LIBS := $(LIBS)
$(builddir)/windows_process_tree.node: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/windows_process_tree.node: TOOLSET := $(TOOLSET)
$(builddir)/windows_process_tree.node:  FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(builddir)/windows_process_tree.node
# Add target alias
.PHONY: windows_process_tree
windows_process_tree: $(builddir)/windows_process_tree.node

# Short alias for building this executable.
.PHONY: windows_process_tree.node
windows_process_tree.node: $(builddir)/windows_process_tree.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/windows_process_tree.node

