cmd_Release/obj.target/watcher/src/binding.o := c++ -o Release/obj.target/watcher/src/binding.o ../src/binding.cc '-DNODE_GYP_MODULE_NAME=watcher' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-DELECTRON_ENSURE_CONFIG_GYPI' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DUSING_ELECTRON_CONFIG_GYPI' '-DV8_COMPRESS_POINTERS' '-DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE' '-DV8_31BIT_SMIS_ON_64BIT_ARCH' '-DV8_ENABLE_SANDBOX' '-DOPENSSL_NO_PINSHARED' '-DOPEN<PERSON>L_THREADS' '-DOPENSSL_NO_ASM' '-DNAPI_DISABLE_CPP_EXCEPTIONS' '-DWATCHMAN' '-DBRUTE_FORCE' '-DFS_EVENTS' '-DKQUEUE' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include -I../../../node-addon-api  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++20 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/watcher/src/binding.o.d.raw   -c
Release/obj.target/watcher/src/binding.o: ../src/binding.cc \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h \
  ../src/wasm/include.h ../../../node-addon-api/napi.h \
  ../../../node-addon-api/napi-inl.h \
  ../../../node-addon-api/napi-inl.deprecated.h ../src/Glob.hh \
  ../src/Event.hh ../src/Backend.hh ../src/Watcher.hh ../src/Debounce.hh \
  ../src/Signal.hh ../src/DirTree.hh ../src/PromiseRunner.hh
../src/binding.cc:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h:
../src/wasm/include.h:
../../../node-addon-api/napi.h:
../../../node-addon-api/napi-inl.h:
../../../node-addon-api/napi-inl.deprecated.h:
../src/Glob.hh:
../src/Event.hh:
../src/Backend.hh:
../src/Watcher.hh:
../src/Debounce.hh:
../src/Signal.hh:
../src/DirTree.hh:
../src/PromiseRunner.hh:
