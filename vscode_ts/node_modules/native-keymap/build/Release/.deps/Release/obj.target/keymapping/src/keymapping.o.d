cmd_Release/obj.target/keymapping/src/keymapping.o := c++ -o Release/obj.target/keymapping/src/keymapping.o ../src/keymapping.cc '-DNODE_GYP_MODULE_NAME=keymapping' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-DELECTRON_ENSURE_CONFIG_GYPI' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DUSING_ELECTRON_CONFIG_GYPI' '-DV8_COMPRESS_POINTERS' '-DV8_COMPRESS_POINTERS_IN_ISOLATE_CAGE' '-DV8_31BIT_SMIS_ON_64BIT_ARCH' '-DV8_E<PERSON><PERSON>E_SANDBOX' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DOPENSSL_NO_ASM' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/35.6.0/deps/v8/include  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++20 -stdlib=libc++ -fno-rtti -fno-exceptions -MMD -MF ./Release/.deps/Release/obj.target/keymapping/src/keymapping.o.d.raw   -c
Release/obj.target/keymapping/src/keymapping.o: ../src/keymapping.cc \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/cppgc/common.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8config.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-array-buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-local-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-handle-base.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-internal.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-memory-span.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-object.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-maybe.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-persistent-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-weak-callback-info.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-primitive.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-data.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-value.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-sandbox.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-traced-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-container.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-context.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-snapshot.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-isolate.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-callbacks.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-promise.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-debug.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-script.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-message.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-embedder-heap.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-exception.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-function-callback.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-microtask.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-statistics.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-unwinder.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-embedder-state-scope.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-date.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-extension.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-external.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-function.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-template.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-initialization.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-platform.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-source-location.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-json.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-locker.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-microtask-queue.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-primitive-object.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-proxy.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-regexp.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-typed-array.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-value-serializer.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-version.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-wasm.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_version.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h \
  ../src/keymapping.h ../src/../deps/chromium/keyboard_codes.h \
  ../src/common.h
../src/keymapping.cc:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/cppgc/common.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8config.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-array-buffer.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-local-handle.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-handle-base.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-internal.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-memory-span.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-object.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-maybe.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-persistent-handle.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-weak-callback-info.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-primitive.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-data.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-value.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-sandbox.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-traced-handle.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-container.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-context.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-snapshot.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-isolate.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-callbacks.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-promise.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-debug.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-script.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-message.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-embedder-heap.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-exception.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-function-callback.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-microtask.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-statistics.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-unwinder.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-embedder-state-scope.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-date.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-extension.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-external.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-function.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-template.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-initialization.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-platform.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-source-location.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-json.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-locker.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-microtask-queue.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-primitive-object.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-proxy.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-regexp.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-typed-array.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-value-serializer.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-version.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/v8-wasm.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_version.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/35.6.0/include/node/node_api_types.h:
../src/keymapping.h:
../src/../deps/chromium/keyboard_codes.h:
../src/common.h:
